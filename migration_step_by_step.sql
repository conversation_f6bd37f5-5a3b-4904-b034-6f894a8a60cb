-- =====================================================
-- STEP-BY-STEP MIGRATION FOR DBEAVER
-- Execute each step one by one and verify results
-- =====================================================

-- STEP 1: BACKUP CHECK
-- Before starting, make sure you have a backup!
-- Run this to see current data:
SELECT 'Current submission count' as info, COUNT(*) as count FROM submission
UNION ALL
SELECT 'Current submission_payment count' as info, COUNT(*) as count FROM submission_payment
UNION ALL  
SELECT 'Submissions with payment_date' as info, COUNT(*) as count FROM submission WHERE payment_date IS NOT NULL;

-- STEP 2: CREATE SUBMISSION_PAYMENT TABLE (if not exists)
CREATE TABLE IF NOT EXISTS `submission_payment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `reference_number` VARCHAR(255) NOT NULL,
    `task_name` VARCHAR(255) NULL,
    `current_reviewer` LONGTEXT NULL,
    `payment_date` DATE NULL,
    `created_at` DATETIME NOT NULL,
    `updated_at` DATETIME NULL,
    `created_by` VARCHAR(255) NULL,
    `updated_by` VARCHAR(255) NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_submission_payment_reference_number` (`reference_number`),
    INDEX `idx_submission_payment_task_name` (`task_name`),
    INDEX `idx_submission_payment_reference_number` (`reference_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- STEP 3: MIGRATE DATA FROM SUBMISSION TO SUBMISSION_PAYMENT
-- This will create submission_payment records for all submissions
INSERT INTO `submission_payment` (
    `reference_number`, 
    `task_name`, 
    `current_reviewer`, 
    `payment_date`, 
    `created_at`, 
    `updated_at`, 
    `created_by`, 
    `updated_by`
)
SELECT 
    s.`reference_number`,
    NULL as `task_name`,  -- Different task_name as per requirement
    NULL as `current_reviewer`,  -- Different current_reviewer as per requirement  
    s.`payment_date`,  -- Migrate payment_date from submission
    NOW() as `created_at`,
    NOW() as `updated_at`,
    'MIGRATION_SCRIPT' as `created_by`,
    'MIGRATION_SCRIPT' as `updated_by`
FROM `submission` s
WHERE s.`reference_number` NOT IN (
    SELECT sp.`reference_number` 
    FROM `submission_payment` sp
)
ON DUPLICATE KEY UPDATE
    `payment_date` = COALESCE(VALUES(`payment_date`), `submission_payment`.`payment_date`),
    `updated_at` = NOW(),
    `updated_by` = 'MIGRATION_SCRIPT';

-- STEP 4: VERIFY MIGRATION SUCCESS
-- Check that all submissions now have corresponding submission_payment records
SELECT 
    'After migration - submission count' as info, 
    COUNT(*) as count 
FROM submission
UNION ALL
SELECT 
    'After migration - submission_payment count' as info, 
    COUNT(*) as count 
FROM submission_payment
UNION ALL
SELECT 
    'Submissions with payment_date' as info, 
    COUNT(*) as count 
FROM submission 
WHERE payment_date IS NOT NULL
UNION ALL
SELECT 
    'SubmissionPayment with payment_date' as info, 
    COUNT(*) as count 
FROM submission_payment 
WHERE payment_date IS NOT NULL;

-- STEP 5: CHECK FOR MISSING RELATIONSHIPS
-- This should return no rows if migration was successful
SELECT 
    s.reference_number,
    s.payment_date as submission_payment_date,
    sp.payment_date as submission_payment_payment_date
FROM submission s 
LEFT JOIN submission_payment sp ON s.reference_number = sp.reference_number 
WHERE sp.reference_number IS NULL;

-- STEP 6: SAMPLE DATA VERIFICATION
-- Check a few records to make sure data migrated correctly
SELECT 
    s.reference_number,
    s.payment_date as original_payment_date,
    sp.payment_date as migrated_payment_date,
    CASE 
        WHEN s.payment_date = sp.payment_date OR (s.payment_date IS NULL AND sp.payment_date IS NULL) 
        THEN 'MATCH' 
        ELSE 'MISMATCH' 
    END as status
FROM submission s
JOIN submission_payment sp ON s.reference_number = sp.reference_number
LIMIT 10;

-- =====================================================
-- AFTER TESTING YOUR APPLICATION WITH THE NEW SCHEMA:
-- =====================================================

-- STEP 7: FINAL STEP - REMOVE PAYMENT_DATE FROM SUBMISSION TABLE
-- ⚠️ WARNING: Only run this after thorough testing!
-- ⚠️ This is irreversible without a backup!
-- 
-- Uncomment the line below when you're ready:
-- ALTER TABLE `submission` DROP COLUMN `payment_date`;

-- =====================================================
-- ROLLBACK SCRIPT (if needed before Step 7)
-- =====================================================
-- If you need to rollback before removing the column:
-- 
-- -- Restore payment_date to submission from submission_payment
-- UPDATE `submission` s
-- INNER JOIN `submission_payment` sp ON s.`reference_number` = sp.`reference_number`
-- SET s.`payment_date` = sp.`payment_date`
-- WHERE sp.`payment_date` IS NOT NULL;
