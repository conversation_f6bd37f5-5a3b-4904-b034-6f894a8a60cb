-- Database Migration Script for Submission Payment Table
-- This script migrates payment_date from submission table to submission_payment table
-- and handles the relationship between submission and submission_payment tables

-- Step 1: Create submission_payment table if it doesn't exist
-- (This table should already exist based on the JPA entity, but including for completeness)
CREATE TABLE IF NOT EXISTS `submission_payment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `reference_number` VARCHAR(255) NOT NULL,
    `task_name` VARCHAR(255) NULL,
    `current_reviewer` LONGTEXT NULL,
    `payment_date` DATE NULL,
    `created_at` DATETIME NOT NULL,
    `updated_at` DATETIME NULL,
    `created_by` VA<PERSON>HAR(255) NULL,
    `updated_by` VARCHAR(255) NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_submission_payment_reference_number` (`reference_number`),
    INDEX `idx_submission_payment_task_name` (`task_name`),
    INDEX `idx_submission_payment_reference_number` (`reference_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 2: Insert records into submission_payment for all submissions that don't have a corresponding record
-- This ensures every submission has a corresponding submission_payment record
INSERT INTO `submission_payment` (
    `reference_number`, 
    `task_name`, 
    `current_reviewer`, 
    `payment_date`, 
    `created_at`, 
    `updated_at`, 
    `created_by`, 
    `updated_by`
)
SELECT 
    s.`reference_number`,
    NULL as `task_name`,  -- Different task_name from submission as per requirement
    NULL as `current_reviewer`,  -- Different current_reviewer from submission as per requirement
    s.`payment_date`,  -- Migrate payment_date from submission
    NOW() as `created_at`,
    NOW() as `updated_at`,
    'MIGRATION_SCRIPT' as `created_by`,
    'MIGRATION_SCRIPT' as `updated_by`
FROM `submission` s
WHERE s.`reference_number` NOT IN (
    SELECT sp.`reference_number` 
    FROM `submission_payment` sp
)
ON DUPLICATE KEY UPDATE
    `payment_date` = COALESCE(VALUES(`payment_date`), `submission_payment`.`payment_date`),
    `updated_at` = NOW(),
    `updated_by` = 'MIGRATION_SCRIPT';

-- Step 3: Update existing submission_payment records with payment_date from submission
-- This handles cases where submission_payment records already exist but need payment_date updated
UPDATE `submission_payment` sp
INNER JOIN `submission` s ON sp.`reference_number` = s.`reference_number`
SET 
    sp.`payment_date` = s.`payment_date`,
    sp.`updated_at` = NOW(),
    sp.`updated_by` = 'MIGRATION_SCRIPT'
WHERE s.`payment_date` IS NOT NULL 
  AND (sp.`payment_date` IS NULL OR sp.`payment_date` != s.`payment_date`);

-- Step 4: Verify the migration
-- Count records to ensure data integrity
SELECT 
    'Submission records' as table_name,
    COUNT(*) as record_count
FROM `submission`
UNION ALL
SELECT 
    'SubmissionPayment records' as table_name,
    COUNT(*) as record_count
FROM `submission_payment`
UNION ALL
SELECT 
    'Submissions with payment_date' as table_name,
    COUNT(*) as record_count
FROM `submission`
WHERE `payment_date` IS NOT NULL
UNION ALL
SELECT 
    'SubmissionPayment with payment_date' as table_name,
    COUNT(*) as record_count
FROM `submission_payment`
WHERE `payment_date` IS NOT NULL;

-- Step 5: Remove payment_date column from submission table
-- WARNING: This is a destructive operation. Make sure to backup your data first!
-- Uncomment the following line after verifying the migration is successful:
-- ALTER TABLE `submission` DROP COLUMN `payment_date`;

-- Step 6: Add foreign key constraint (optional, for referential integrity)
-- This ensures that submission_payment.reference_number always references a valid submission
-- Uncomment if you want to enforce referential integrity:
-- ALTER TABLE `submission_payment` 
-- ADD CONSTRAINT `fk_submission_payment_reference` 
-- FOREIGN KEY (`reference_number`) REFERENCES `submission`(`reference_number`) 
-- ON DELETE CASCADE ON UPDATE CASCADE;

-- Migration completed successfully!
-- Remember to:
-- 1. Test the migration on a development/staging environment first
-- 2. Backup your production database before running this script
-- 3. Update your application code to use SubmissionPayment entity for payment_date operations
-- 4. Uncomment the DROP COLUMN statement after verifying everything works correctly
